import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElOption,
  ElSelect,
  ElSwitch,
  ElUpload,
  ElIcon,
  ElCollapse,
  ElCollapseItem
} from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { useForm } from './hooks'
import ModalBottom from '@/components/modal-bottom'
import { AdminModelListItem, AdminModelNeedPool, SupportMsgType } from '@/api/adminModel/type'
import { useUpload } from './useUpload'
import TokenPrice from './token-price.vue'
export default defineComponent({
  props: {
    modelValue: Boolean,
    adminModelListItem: {
      type: Object as PropType<AdminModelListItem>,
      default: () => ({})
    }
  },
  emits: ['update:modelValue', 'refetch'],
  setup(props, { emit }) {
    const { adminModelListItem } = toRefs(props)
    const visible = useModel(props, 'modelValue')
    const refetch = () => emit('refetch')
    const { fileList, removeChange } = useUpload()
    const {
      formRef,
      form,
      rules,
      resetFormData,
      submit,
      title,
      modelList,
      adminModelTypeOptions,
      adminModelSourceOptions,
      adminModelOptions,
      selectForm
    } = useForm(refetch, visible, adminModelListItem, fileList)
    const isOpenAi = computed(() => form.modelCompanyId === 1)
    const isAudioType = computed(() => {
      const { supportMsgType = [] } = form
      return supportMsgType.includes(SupportMsgType.audio)
    })
    const isSupportBatch = computed(() => {
      const { withBatch = false } = form
      return withBatch
    })
    return () => (
      <ElDialog title={title.value} v-model={visible.value} width={800}>
        <ElForm labelPosition="left" labelWidth={220} model={form} ref={formRef} rules={rules}>
          <ElFormItem label="模型名称" prop="name">
            <ElInput v-model={form.name} />
          </ElFormItem>
          <ElFormItem label="模型类型" prop="type">
            <ElSelect v-model={form.type}>
              {adminModelTypeOptions.map((item) => (
                <ElOption key={item.name} label={item.value} value={+item.name} />
              ))}
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="模型来源" prop="source">
            <ElSelect v-model={form.source}>
              {adminModelSourceOptions.map((item) => (
                <ElOption key={item.name} label={item.value} value={+item.name} />
              ))}
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="厂商" prop="modelCompanyId">
            <ElSelect
              filterable
              onChange={(val) => {
                if (
                  !adminModelOptions.value?.companyList
                    .find((item) => item.id === val)
                    ?.modelCategoryList.find((item) => item.id === form.parentId)
                )
                  form.parentId = undefined
              }}
              v-model={form.modelCompanyId}>
              {adminModelOptions.value?.companyList.map((item) => (
                <ElOption key={item.id} label={item.name} value={item.id} />
              ))}
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="模型组" prop="parentId">
            <ElSelect clearable filterable v-model={selectForm.parentId.value}>
              {modelList.value?.map((item) => (
                <ElOption key={item.id} label={item.name} value={item.id} />
              ))}
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="使用场景" prop="scene">
            <ElSelect filterable v-model={form.scene}>
              {adminModelOptions.value?.sceneList.map((item) => (
                <ElOption key={item} label={item} value={item} />
              ))}
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="是否需要池子" prop="needPool">
            <ElSwitch
              activeValue={AdminModelNeedPool.True}
              inactiveValue={AdminModelNeedPool.False}
              v-model={form.needPool}
            />
          </ElFormItem>
          <ElFormItem label="是否支持深度思考" prop="supportThinking">
            <ElSwitch v-model={form.supportThinking} />
          </ElFormItem>
          <ElFormItem label="模型描述" prop="desc">
            <ElInput type="textarea" v-model={form.desc} />
          </ElFormItem>
          <ElFormItem label="模型logo" prop="logoPath">
            <ElUpload
              auto-upload={false}
              class={{ 'hidden-upload-trigger': fileList.value.length, 'avatar-uploader': true }}
              limit={1}
              list-type="picture-card"
              on-remove={removeChange}
              show-file-list={true}
              v-model:file-list={fileList.value}>
              <ElIcon>
                <Plus />
              </ElIcon>
            </ElUpload>
          </ElFormItem>
          <ElFormItem label="模型访问url" prop="url">
            <ElInput v-model={form.url} />
          </ElFormItem>
          <ElFormItem label="免费模型" prop="isFree">
            <ElSwitch activeValue={1} inactiveValue={0} v-model={form.isFree} />
          </ElFormItem>
          <ElFormItem label="最大输入token数量" prop="maxInTokens">
            <ElInputNumber controls={false} min={0} precision={0} v-model={form.maxInTokens} />
          </ElFormItem>
          <ElFormItem label="最大输出token数量" prop="maxTokens">
            <ElInputNumber controls={false} min={0} precision={0} v-model={form.maxTokens} />
          </ElFormItem>
          <ElFormItem label="总最大token数量" prop="contextWindow">
            <ElInputNumber controls={false} min={0} precision={0} v-model={form.contextWindow} />
          </ElFormItem>
          <ElFormItem label="是否是国内模型" prop="useCn">
            <ElSwitch v-model={form.useCn} />
          </ElFormItem>
          <ElFormItem label="是否不支持调用" prop="forbidRequest">
            <ElSwitch v-model={form.forbidRequest} />
          </ElFormItem>
          <ElFormItem label="支持消息格式" prop="supportMsgType">
            <ElSelect filterable multiple v-model={form.supportMsgType}>
              {adminModelOptions.value?.supportMsgTypeList.map((item) => (
                <ElOption key={item} label={item} value={item} />
              ))}
            </ElSelect>
          </ElFormItem>
          <ElFormItem class="hidden-label">
            <ElCollapse class="w-full">
              <ElCollapseItem title="模型限制粒度">
                {adminModelOptions.value?.limitTypeList.map((type) => (
                  <section class="w-1/4 inline-block box-border odd:pr-5 even:pl-5" key={type}>
                    <ElFormItem label={type} labelWidth={50} prop={type}>
                      <section class="flex gap-4">
                        <ElSwitch
                          model-value={!!form[type]}
                          onChange={(val) => {
                            form[type] = val ? 1 : undefined
                          }}
                        />
                      </section>
                    </ElFormItem>
                  </section>
                ))}
              </ElCollapseItem>
            </ElCollapse>
          </ElFormItem>
          <ElFormItem class="hidden-label" prop="cost">
            <TokenPrice isAudio={isAudioType.value} title="每1Mtoken价格" v-model={form.cost} />
          </ElFormItem>
          <ElFormItem class="hidden-label" prop="realCost">
            <TokenPrice
              isAudio={isAudioType.value}
              title="每1Mtoken核销价格"
              v-model={form.realCost}
            />
          </ElFormItem>
          {isOpenAi.value && (
            <ElFormItem class="hidden-label" prop="shareCost">
              <TokenPrice
                isAudio={isAudioType.value}
                title="折扣AZ每1Mtoken分摊价格"
                v-model={form.shareCost}
              />
            </ElFormItem>
          )}
          {isSupportBatch.value && (
            <section>
              <ElFormItem class="hidden-label" prop="batchCost">
                <TokenPrice
                  batch={true}
                  isAudio={isAudioType.value}
                  title="跑批每1Mtoken价格"
                  v-model={form.batchCost}
                />
              </ElFormItem>
              <ElFormItem class="hidden-label" prop="batchRealCost">
                <TokenPrice
                  batch={true}
                  isAudio={isAudioType.value}
                  title="跑批每1Mtoken核销价格"
                  v-model={form.batchRealCost}
                />
              </ElFormItem>
              <ElFormItem class="hidden-label" prop="batchShareCost">
                <TokenPrice
                  batch={true}
                  isAudio={isAudioType.value}
                  title="跑批每1Mtoken分摊价格"
                  v-model={form.batchShareCost}
                />
              </ElFormItem>
            </section>
          )}
        </ElForm>
        <ModalBottom onConfirm={submit} onReset={resetFormData} />
      </ElDialog>
    )
  }
})
