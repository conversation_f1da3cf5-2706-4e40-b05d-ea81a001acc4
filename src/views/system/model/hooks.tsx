import {
  getAdminModelList,
  useDelAdminModel,
  useGetAdminModelOptions,
  useModelControl
} from '@/api/adminModel'
import DelButton from '@/components/del-button'
import { generateTableList } from '@znzt-fe/utils'
import { useList, useElModal, useElForm } from '@znzt-fe/hooks'
import { ElButton, ElButtonGroup, ElMessage, ElMessageBox, ElTag } from 'element-plus'
import {
  AdminModelListItem,
  AdminModelStatus,
  AdminModelListParams,
  GetAdminModelOptionsRet,
  ModelControlOp,
  ModelControlParams
} from '@/api/adminModel/type'
import { Options } from '@znzt-fe/declare'
import { useOptions } from './modal/hooks'
import { noop } from 'lodash-es'
import useCommonStore from '@/store/common'

export const useForm = () => {
  const initData: AdminModelListParams = {
    companyId: undefined,
    parentId: undefined
  }
  const { data: adminModelOptions, refetch: refetchGetAdminModelOptions } =
    useGetAdminModelOptions()
  const modelList = computed(() =>
    adminModelOptions?.value?.companyList.reduce((pre, now) => {
      if (form.companyId) {
        now.id === form.companyId && pre.push(...now.modelCategoryList)
      } else {
        pre.push(...now.modelCategoryList)
      }
      return pre
    }, [] as Options[])
  )
  const { form } = useElForm(initData)
  return {
    form,
    adminModelOptions,
    modelList,
    refetchGetAdminModelOptions
  }
}

export const useTable = (
  openModal: (id: string) => void,
  query: AdminModelListParams,
  adminModelOptions: Ref<GetAdminModelOptionsRet | undefined>
) => {
  const {
    listParams,
    isLoading,
    refetchData,
    mutate: getAdminModelListMutate
  } = useList<AdminModelListItem>({
    getList: getAdminModelList,
    query
  })
  const commonStore = useCommonStore()
  const { mutate: modelControlMutateOrigin } = useModelControl({
    onSuccess: () => {
      ElMessage.success('操作成功')
      getAdminModelListMutate()
      commonStore.getConfig()
    }
  })
  const modelControlMutate = (params: ModelControlParams) => {
    ElMessageBox.confirm(
      `确认执行${params.op === ModelControlOp['offline'] ? '下线' : '上线'}操作么？`,
      '警告',
      {
        confirmButtonText: '确认',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
      .then(() => modelControlMutateOrigin(params))
      .catch(noop)
  }
  const forbiddenMutate = async (id: number) => {
    await ElMessageBox.confirm(`将批量禁用所有业务线对此模型的调用权限`, '警告', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    })
    modelControlMutateOrigin({ id, op: ModelControlOp['forbidden'] })
  }

  const { mutate: delAdminModel } = useDelAdminModel({
    onSuccess: refetchData
  })
  const { adminModelNeedPoolMap } = useOptions()
  const tableColumn = generateTableList<AdminModelListItem>([
    {
      width: '200',
      prop: 'name',
      label: '名称'
    },
    {
      prop: 'modelCompanyName',
      label: '厂商'
    },
    {
      prop: 'scene',
      label: '模型场景'
    },
    {
      width: '80',
      prop: 'needPool',
      label: '是否需要池子',
      slots: (scope) => adminModelNeedPoolMap[scope.row.needPool]
    },
    {
      prop: 'status',
      label: '模型状态',
      slots: (scope) => {
        switch (scope.row.status) {
          case AdminModelStatus.Effective:
            return (
              <ElTag disableTransitions effect="dark">
                有效
              </ElTag>
            )
          case AdminModelStatus.Invalid:
            return (
              <ElTag disableTransitions effect="dark" type="warning">
                无效
              </ElTag>
            )
          case AdminModelStatus.offline:
            return (
              <ElTag disableTransitions effect="dark" type="danger">
                下线
              </ElTag>
            )
          case AdminModelStatus.forbidden:
            return (
              <ElTag disableTransitions effect="dark" type="danger">
                禁用
              </ElTag>
            )
        }
      }
    },
    {
      prop: 'parentId',
      label: '模型组名称',
      slots: (scope) =>
        adminModelOptions.value?.companyList
          .find((item) => item.id === scope.row.modelCompanyId)
          ?.modelCategoryList.find((item) => item.id === scope.row.parentId)?.name || '-'
    },
    {
      label: '输入/输出每M价格',
      prop: 'tokenPrice',
      slots: (scope) => {
        const { cost = {} } = scope.row || {}
        const { prompt = '-', completion = '-' } = cost
        return (
          <div>
            {prompt}/{completion}
          </div>
        )
      }
    },
    {
      prop: 'contextWindow',
      label: '最大窗口token',
      slots: (scope) => {
        const { maxTokens = '-', contextWindow = '-', maxInTokens = '-' } = scope.row || {}
        return (
          <div>
            {maxInTokens}/{maxTokens}/{contextWindow}
          </div>
        )
      }
    },
    {
      prop: 'onlineTime',
      label: '上线日期'
    },
    {
      prop: 'offlineTime',
      label: '下线日期'
    },
    {
      prop: 'operation',
      label: '操作',
      slots: (scope) => (
        <ElButtonGroup>
          <ElButton link onClick={() => openModal(scope.row.id + '')} type="primary">
            编辑
          </ElButton>
          {scope.row.status !== AdminModelStatus.forbidden &&
            (scope.row.status === AdminModelStatus.offline ? (
              <ElButton
                link
                onClick={() =>
                  modelControlMutate({ id: scope.row.id, op: ModelControlOp['online'] })
                }
                type="primary">
                上线
              </ElButton>
            ) : (
              <ElButton
                link
                onClick={() =>
                  modelControlMutate({ id: scope.row.id, op: ModelControlOp['offline'] })
                }
                type="primary">
                下线
              </ElButton>
            ))}
          <DelButton
            confirmText="删除可能影响线上使用，确认删除？"
            onClick={() => delAdminModel({ id: scope.row.id })}>
            删除
          </DelButton>
          {scope.row.status === AdminModelStatus.offline && (
            <ElButton link onClick={() => forbiddenMutate(scope.row.id)} type="danger">
              禁用
            </ElButton>
          )}
        </ElButtonGroup>
      )
    }
  ])
  return {
    isLoading,
    tableColumn,
    listParams,
    refetchData,
    getAdminModelListMutate
  }
}

export const useModal = useElModal
