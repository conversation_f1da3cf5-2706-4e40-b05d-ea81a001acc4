<template>
  <SearchContainer>
    <section class="flex gap-3">
      <el-form-item>
        <el-input v-model.trim="state.query.uname" placeholder="项目前缀" clearable>
          <template #append>
            <el-button type="primary" @click="queryList">搜索</el-button>
          </template>
        </el-input>
      </el-form-item>
      <el-form-item class="min-w-24">
        <el-select v-model="state.query.accountType" @change="queryList">
          <el-option label="用户" :value="1" />
          <el-option label="服务号" :value="2" />
        </el-select>
      </el-form-item>
    </section>
    <section class="">
      <el-button type="primary" @click="() => (state.showSecretDialog = true)">新建服务账号</el-button>
      <el-button type="primary" @click="refresh">刷新</el-button>
      <el-button type="danger" @click="batchDelHandler()" :disabled="!state.selection.length"
        v-if="isPassed">批量删除</el-button>
      <el-button type="primary" @click="passBatch()" :disabled="!state.selection.length"
        v-if="!isPassed">批量通过</el-button>
      <el-button type="primary" @click="addItem" v-if="isPassed">新建用户</el-button>
    </section>
  </SearchContainer>
  <section class="table-container">
    <el-table :data="state.list" style="width: 100%" :show-overflow-tooltip="true"
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" :selectable="selectable" />
      <el-table-column prop="uname" min-width="150" label="用户/服务" />
      <el-table-column prop="zhName" label="中文名" />
      <el-table-column prop="settleReason" label="申请原因" v-if="isGuestBus" />
      <el-table-column prop="status" width="100" label="状态" :formatter="statusFormatter" />
      <el-table-column :show-overflow-tooltip="false" prop="createTime" width="150px" :formatter="timeFormat"
        label="创建时间" />
      <el-table-column :show-overflow-tooltip="false" prop="updateTime" width="150px" :formatter="timeFormat"
        label="修改时间" />
      <el-table-column prop="apiCntLimit" label="请求限制" v-if="isPassed" />
      <el-table-column prop="secretKey" label="api key" v-if="isPassed">
        <template #default="scope">
          <el-button link type="primary" @click="showRealSecretKey(scope.row)">{{ scope.row.secretKey }}</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="roleName" label="用户角色">
        <template #default="scope">
          {{ scope.row.roleId === RoleId.Service ? '服务账号' : scope.row.roleName }}
        </template>
      </el-table-column>
      <el-table-column fixed="right" label="操作" width="230">
        <template #default="scope">
          <section className="action-btn">
            <DelButton @click="deleteBatch([scope.row.relationId])" :confirmText="getDelText(scope.row)" v-if="
              (isPassed && scope.row.roleId === RoleId.User) ||
              scope.row.roleId === RoleId.Service
            " size="small"></DelButton>
            <el-button text type="primary" size="small" @click="passBatch([scope.row.relationId])"
              v-if="!isPassed">通过</el-button>
            <el-button text type="primary" size="small" @click="clickLimit(scope.row)" v-if="isPassed">调控</el-button>
            <el-button text type="primary" size="small" @click="updateRole(scope.row)"
              v-if="isPassed && scope.row.roleId !== RoleId.Service">修改角色</el-button>
            <ElButton link @click="resetUserSk(scope.row)" type="danger"> 重置api key </ElButton>
          </section>
        </template>
      </el-table-column>
    </el-table>
    <Pagination v-model:pageNum="state.pageInfo.pageNum" v-model:page-size="state.pageInfo.pageSize"
      :total="state.pageInfo.total" @refresh="getList">
    </Pagination>
  </section>
  <Add v-if="state.show" @close="closeDialog"></Add>
  <AddSecret v-model="state.showSecretDialog" :refetch="queryList" />
  <ApiLimit v-if="state.showLimitDialog" @close="closeDialog" :detail="state.detail"></ApiLimit>
  <RoleDialog v-if="state.showUpdateDialog" @close="closeDialog" :detail="state.detail"></RoleDialog>
</template>

<script lang="ts" setup>
import { Pagination } from '@znzt-fe/components'
import $http from '@/api'
import useUserStore from '@/store/user'
import { ElMessage, ElMessageBox } from 'element-plus'
import Add from './add.vue'
import AddSecret from './addSecret'
import ApiLimit from './apiLimit.vue'
import RoleDialog from './role.vue'
import DelButton from '@/components/del-button'
import SearchContainer from '@/components/search-container'
import { RoleId } from '@/api/user/type'
import { timeFormat } from '@/utils'
const props = defineProps({
  status: {
    type: Number,
    default: 0
  }
})
const userStore = useUserStore()
const getDelText = (row: any) => {
  const { roleId } = row
  const text =
    roleId === RoleId.Service
      ? `删除后，服务账号"${row.zhName || row.uname}"的API Key将失效。请务必确认无线上业务使用。`
      : `删除后，账号"${row.zhName || row.uname}"的API Key将失效。请务必确认无线上业务使用。`
  return text
}

const resetUserSk = (userInfo: any) => {
  const isServiceApiKey = userInfo.roleId === RoleId.Service
  const text = isServiceApiKey
    ? `注意：重置后，服务账号"${userInfo.zhName || userInfo.uname
    }"的API Key将失效。请务必确认无线上业务使用。`
    : `<p>注意：重置可能影响<strong>多个业务线</strong>。重置后，当前业务线以及<strong>其他业务线</strong>都将无法使用"${userInfo.zhName || userInfo.uname
    }"的API Key。请务必确认无线上业务使用。</p>`
  ElMessageBox.confirm(text, '重置api key', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    dangerouslyUseHTMLString: true
  }).then(async () => {
    const params = {
      uname: userInfo.uname
    }
    await $http.resetSecret({
      ...params
    })
    ElMessage.success({
      message: '重置api key成功',
      duration: 3000
    })
    getList()
    if (userInfo.uname === userStore.name) {
      userStore.getUserInfo()
    }
  })
}
const isPassed = props.status === 2
const isGuestBus = computed(() => {
  const { current } = userStore
  return current.businessId === 1
})

const state: any = reactive({
  query: {
    uname: '',
    accountType: 2
  },
  list: [],
  pageInfo: {
    pageSize: 20,
    pageNum: 1,
    total: 0
  },
  dialog: false,
  detail: {},
  selection: [],
  show: false,
  showLimitDialog: false,
  showUpdateDialog: false,
  showSecretDialog: false,
})
const queryList = () => {
  state.pageInfo.pageNum = 1
  getList()
}
const getList = async () => {
  const params = {
    ...state.query,
    ...state.pageInfo,
    status: props.status,
    businessId: userStore.current.businessId,
    withUsage: true,
    withSecret: true
  }
  const { list = [], total } = await $http.getUserList(params)

  state.list = list
  state.pageInfo.total = +total
}
queryList()
const addItem = async () => {
  state.show = true
}
const handleSelectionChange = (val: any[]) => {
  state.selection = val.map((item: any) => item.relationId)
}
const clickLimit = (detail: any) => {
  state.detail = detail
  state.showLimitDialog = true
}
const batchDelHandler = async () => {
  const text = '删除后，选中账号的API Key将失效。请务必确认无线上业务使用。'
  const pass = await ElMessageBox.confirm(text, '批量删除提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).catch(() => false)
  if (!pass) {
    return
  }
  deleteBatch()
}
const deleteBatch = (ids = state.selection) => {
  controlUser(ids, 3)
}
const passBatch = (ids = state.selection) => {
  controlUser(ids, 1)
}
const controlUser = async (relationIds: string[] = [], actionControl = 1) => {
  await $http.controlUser({
    actionControl,
    relationIds,
    businessId: userStore.current?.businessId
  })
  const message = actionControl === 1 ? '审批通过!5分钟后生效' : '删除成功!5分钟后生效'
  ElMessage.success(message)
  queryList()
}
const statusFormatter = (row: any) => {
  return row.status === 1 ? '待审核' : '审核通过'
}
const closeDialog = (refresh = false) => {
  state.show = false
  state.showLimitDialog = false
  state.showUpdateDialog = false
  if (refresh) {
    getList()
  }
}
const selectable = (row: any) => {
  return row.roleId !== RoleId.BusAdmin && row.roleId !== RoleId.Service
}
const refresh = () => {
  state.query.uname = ''
  state.pageInfo.pageNum = 1
  getList()
}
const updateRole = (detail: any) => {
  state.detail = detail
  state.showUpdateDialog = true
}
const showRealSecretKey = (row)=>{
  console.log('hhaaah', row)
}
</script>
<style scoped lang="less">
.table-container {
  border-radius: 4px;
  background: white;
  padding: 12px;
}

.action-btn {
  .el-button {
    margin: 0;
    padding: 3px;
    height: 20px;
    font-size: 13px;
  }
}
</style>
