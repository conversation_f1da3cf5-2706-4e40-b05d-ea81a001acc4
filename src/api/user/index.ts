import axios from '@/plugin/axios'
import { MutationFn } from '@znzt-fe/axios'
import { afterDecorator } from '@znzt-fe/utils'
import { LoginInfoParams, LoginInfoRet, SearchUserRet, SettleBussParams, UserSeceretParams } from './type'

const { get, post, mutationGet, mutationPost } = axios('user')

/**  用户个人登录信息 */
export const getUserInfo = afterDecorator(
  (data: LoginInfoParams) => get<LoginInfoRet>('logininfo', data),
  (data) => {
    window.aegis?.setConfig({
      uin: data.uname
    })
    window.aegis?.ready()
    window.aegis?.setConfig({
      reportImmediately: true
    })
    window.apmPlus?.('config', { userId: data.uname })
    window.apmPlus?.('start')
  }
)

/**  用户列表 */
export const getUserList = (data: any) => get<any>('list', data)

/**  用户搜索 */
export const searchUser = (data: any) => get<any>('ipssearch', data)

/**  用户搜索 */
export const useSearchUser: MutationFn<{}, SearchUserRet> = (options) =>
  mutationGet('ipssearch', options)

/**  修改用户角色 */
export const updateRole = (data: any) => post<any>('updaterole', data)

/**  用户退出 */
export const logout = () => post<any>('logout')

/**  用户申请入驻 */
export const applySettle = (data: any) => post<any>('applysettle', data)

/**  调控用户 */
export const controlUser = (data: any) => post<any>('control', data)

/**  用户入驻 */
export const userSettle = (data: any) => post<any>('settle', data)

/**  重置用户密钥 */
export const resetSecret = (data: any) => {
  return post<any>('resetsecret', data)
}

/** 增加业务线密钥 */
export const useSettleBuss: MutationFn<SettleBussParams> = (options) =>
  mutationPost('settlebuss', options)

/** 获取用户secret */
export const getUserSecret: MutationFn<UserSeceretParams> = (options) =>
  mutationGet('getusersecret', options)

