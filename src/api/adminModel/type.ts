import { Empty, Id, Options, UniversalListResult } from '@znzt-fe/declare'

export interface AdminModelListParams {
  companyId: Empty
  parentId: Empty
}

export enum SupportMsgType {
  image = 'img',
  audio = 'audio'
}

export type AdminModelListRet = UniversalListResult<AdminModelListItem>
export interface AdminModelListItem {
  id: number
  name: string // 模型名称
  type: AdminModelType // 模型类型 1 正式 2  内测
  parentId: number // 父模型id
  needPool: AdminModelNeedPool
  status: AdminModelStatus
  source: AdminModelSource // 模型来源 1 预置 2 自建
  modelCompanyId: number // 厂商Id
  modelCompanyName: string
  desc: string
  createTime: string
  updateTIme: string
  url?: string
  maxTokens?: number
  maxInTokens?: number
  contextWindow?: number
  useCn?: boolean // 国内模型
  forbidRequest?: boolean
  supportMsgType?: string[]
  promptPricesPer1MillionTokens?: number
  completionPricesPer1MillionTokens?: number
  logoPath?: string
  logoUrl?: string
  cost?: TokenPriceParams
}

export const enum AdminModelType {
  External = 1,
  Internal
}

export const enum AdminModelStatus {
  Effective = 1,
  Invalid,
  offline,
  forbidden
}

export const enum AdminModelSource {
  Preset = 1,
  SelfBuild
}

export type DelAdminModelParams = Id<number>

export interface limitTypeList {
  ipm?: Empty
  qps?: Empty
  rpm?: Empty
  tpm?: Empty
}
export interface GetAdminModelOptionsRet {
  companyList: Array<Options & ModelCategoryList>
  sceneList: string[]
  supportMsgTypeList: string[]
  limitTypeList: Array<keyof limitTypeList>
}
export type ModelCategoryList = {
  modelCategoryList: Array<Options>
}

export const enum AdminModelNeedPool {
  True = 1,
  False
}

export interface TokenPriceParams {
  prompt?: Empty
  completion?: Empty
  inputText?: Empty
  inputAudio?: Empty
  inputTextCache?: Empty
  inputAudioCache?: Empty
  outputText?: Empty
  outputAudio?: Empty
}

export interface AddAdminModelParams {
  name: string // 模型名称
  type: AdminModelType // 模型类型 1 正式 2  内测
  source: AdminModelSource // 模型来源 1 预置 2 自建
  parentId: Empty // 父模型id
  modelCompanyId: Empty // 厂商Id
  needPool: AdminModelNeedPool // 是否需要池子 1 需要 2 不需要 默认2
  supportThinking: false // 是否支持思考
  desc: string // 模型描述
  scene: string // 使用场景
  url?: string // 模型访问url
  maxTokens?: Empty // 最大token数量
  maxInTokens?: Empty // 最大输入token数量
  contextWindow?: Empty // 最大窗口token数量
  useCn?: boolean // 国内模型
  forbidRequest?: boolean // 是否不支持调用
  supportMsgType?: string[] // 支持消息格式
  withBatch?: boolean // 是否支持跑批
  cost?: TokenPriceParams //  1Mtoken价格
  realCost?: TokenPriceParams // 1Mtoken核销价格
  shareCost?: TokenPriceParams //  1Mtoken分摊价格
  batchCost?: TokenPriceParams //  跑批1Mtoken价格
  batchRealCost?: TokenPriceParams //   跑批1Mtoken核销价格
  batchShareCost?: TokenPriceParams //   跑批1Mtoken分摊价格
  logoPath?: string // logo路径
  ipm?: Empty
  qps?: Empty
  rpm?: Empty
  tpm?: Empty
  isFree?: number //模型是否免费
}

export interface UpdateAdminModelParams {
  id: number
  name: string // 模型名称
  type: AdminModelType // 模型类型 1 正式 2  内测
  source: AdminModelSource // 模型来源 1 预置 2 自建
  parentId: Empty // 父模型id
  modelCompanyId: Empty // 厂商Id
  needPool: AdminModelNeedPool // 是否需要池子 1 需要 2 不需要 默认2
  desc: string // 模型描述
  scene: string // 使用场景
  url?: string // 模型访问url
  maxTokens?: Empty // 最大token数量
  maxInTokens?: Empty // 最大输入token数量
  contextWindow?: Empty // 最大窗口token数量
  useCn?: boolean // 国内模型
  forbidRequest?: boolean // 是否不支持调用
  supportMsgType?: string[] // 支持消息格式
  promptPricesPer1MillionTokens?: Empty // 每1千输入token价格
  completionPricesPer1MillionTokens?: Empty // 每1千输出token价格
  rpm?: Empty
  tpm?: Empty
  ipm?: Empty
  qps?: Empty
}

export interface ModelControlParams {
  id: number
  op: ModelControlOp
}
export const enum ModelControlOp {
  online = 1,
  offline,
  forbidden
}
